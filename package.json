{"name": "node-practice", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "dotenv": "^17.0.1", "express": "^5.1.0", "mongoose": "^8.16.1"}, "devDependencies": {"nodemon": "^3.1.10"}}