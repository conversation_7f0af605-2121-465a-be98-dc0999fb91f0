const Usermodel= require('../models/User');

exports.createUser= async (req, res) => {
    try {
        const { username, email, password }= req.body;
        const user= new Usermodel({ username, email, password });
        await user.save();
        res.status(200).json(user);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

exports.getAllUsers= async (req, res) => {
    try {
        const users= await Usermodel.find();
        res.status(200).json(users);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};